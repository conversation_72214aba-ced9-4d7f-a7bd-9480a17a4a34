import {
  getGeographicalData
} from "@/api/base-config";

const state = {
  // 地理数据（省市区）
  geographicalData: null,

  // 加载状态
  loading: {
    geographicalData: false
  },

  // 错误信息
  errors: {
    geographicalData: null
  },

  // 数据加载时间戳
  timestamps: {
    geographicalData: 0
  }
};

const mutations = {
  // 设置地理数据
  SET_GEOGRAPHICAL_DATA(state, data) {
    state.geographicalData = data;
    state.timestamps.geographicalData = Date.now();
  },

  // 设置加载状态
  SET_LOADING(state, { type, loading }) {
    state.loading[type] = loading;
  },

  // 设置错误信息
  SET_ERROR(state, { type, error }) {
    state.errors[type] = error;
  },

  // 清除错误信息
  CLEAR_ERROR(state, type) {
    state.errors[type] = null;
  },

  // 清除所有错误信息
  CLEAR_ALL_ERRORS(state) {
    Object.keys(state.errors).forEach(key => {
      state.errors[key] = null;
    });
  }
};

const actions = {
  // 加载地理数据
  async loadGeographicalData({ commit }, forceRefresh = false) {
    // 如果数据已存在且不强制刷新，则直接返回
    if (!forceRefresh && state.geographicalData) {
      return state.geographicalData;
    }

    commit("SET_LOADING", { type: "geographicalData", loading: true });
    commit("CLEAR_ERROR", "geographicalData");

    try {
      const response = await getGeographicalData();
      if (response.code === 0) {
        commit("SET_GEOGRAPHICAL_DATA", response.data);
        return response.data;
      } else {
        const error = response.msg || "加载地理数据失败";
        commit("SET_ERROR", { type: "geographicalData", error });
        throw new Error(error);
      }
    } catch (error) {
      const errorMsg = error.message || "加载地理数据失败";
      commit("SET_ERROR", { type: "geographicalData", error: errorMsg });
      console.error("加载地理数据失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", { type: "geographicalData", loading: false });
    }
  },

  // 刷新地理数据
  async refreshGeographicalData({ dispatch }) {
    try {
      await dispatch("loadGeographicalData", true);
      console.log("地理数据刷新完成");
    } catch (error) {
      console.error("刷新地理数据时发生错误:", error);
      throw error;
    }
  }
};

const getters = {
  // 获取地理数据
  geographicalData: state => state.geographicalData,

  // 获取省份列表
  provinces: state => {
    if (!state.geographicalData || !state.geographicalData.provinces) {
      return [];
    }
    return state.geographicalData.provinces;
  },

  // 获取城市列表
  cities: state => {
    if (!state.geographicalData || !state.geographicalData.cities) {
      return [];
    }
    return state.geographicalData.cities;
  },

  // 获取区县列表
  districts: state => {
    if (!state.geographicalData || !state.geographicalData.districts) {
      return [];
    }
    return state.geographicalData.districts;
  },

  // 根据省份ID获取城市列表
  getCitiesByProvinceId: state => provinceId => {
    if (!state.geographicalData || !state.geographicalData.cities) {
      return [];
    }
    return state.geographicalData.cities.filter(
      city => city.province_id === provinceId || city.province_code === provinceId
    );
  },

  // 根据城市ID获取区县列表
  getDistrictsByCityId: state => cityId => {
    if (!state.geographicalData || !state.geographicalData.districts) {
      return [];
    }
    return state.geographicalData.districts.filter(
      district => district.city_id === cityId || district.city_code === cityId
    );
  },

  // 获取加载状态
  isLoading: state => type => state.loading[type] || false,

  // 获取错误信息
  getError: state => type => state.errors[type],

  // 检查是否有任何加载中的数据
  hasAnyLoading: state => {
    return Object.values(state.loading).some(loading => loading);
  },

  // 检查是否有任何错误
  hasAnyError: state => {
    return Object.values(state.errors).some(error => error !== null);
  },

  // 获取数据加载时间戳
  getTimestamp: state => type => state.timestamps[type]
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
